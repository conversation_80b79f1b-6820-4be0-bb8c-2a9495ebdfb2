<template>
  <div class="template-editor-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft" size="large">
          返回
        </el-button>
        <div class="template-info">
          <h1 class="template-title">{{ templateName || '新建模板' }}</h1>
          <p class="template-description">编辑医院病例报告模板</p>
        </div>
      </div>
      <div class="header-right">
        <el-button @click="handlePreview" :icon="View" size="large">
          预览
        </el-button>
        <el-button @click="handleSave" type="primary" :icon="Check" size="large">
          保存模板
        </el-button>
      </div>
    </div>

    <!-- 编辑器区域 -->
    <div class="editor-container">
      <TemplateEditor
        v-model="templateContent"
        :height="editorHeight"
        :editable="true"
        placeholder="请开始编辑您的病例报告模板..."
        @save="handleSave"
        @change="handleContentChange"
      />
    </div>

    <!-- 预览弹窗 -->
    <el-dialog
      v-model="previewVisible"
      title="模板预览"
      width="80%"
      :before-close="handleClosePreview"
      class="preview-dialog"
    >
      <div class="preview-content" v-html="templateContent"></div>
      <template #footer>
        <el-button @click="handleClosePreview">关闭</el-button>
        <el-button type="primary" @click="handlePrint">打印预览</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, View, Check } from '@element-plus/icons-vue'
import TemplateEditor from './components/TemplateEditor.vue'

defineOptions({
  name: 'TemplateEditorPage'
})

const route = useRoute()
const router = useRouter()

// 模板信息
const templateId = computed(() => route.params.id as string)
const templateName = computed(() => route.query.name as string)

// 编辑器状态
const templateContent = ref('')
const previewVisible = ref(false)
const hasUnsavedChanges = ref(false)

// 计算编辑器高度
const editorHeight = computed(() => {
  return window.innerHeight - 200 // 减去头部和其他元素的高度
})

// 返回上一页
const goBack = async () => {
  if (hasUnsavedChanges.value) {
    try {
      await ElMessageBox.confirm(
        '您有未保存的更改，确定要离开吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消
    }
  }
  router.back()
}

// 保存模板
const handleSave = async () => {
  try {
    // 这里应该调用API保存模板
    console.log('保存模板:', {
      id: templateId.value,
      name: templateName.value,
      content: templateContent.value
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    hasUnsavedChanges.value = false
    ElMessage.success('模板保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
    console.error('保存模板失败:', error)
  }
}

// 预览模板
const handlePreview = () => {
  if (!templateContent.value.trim()) {
    ElMessage.warning('模板内容为空，无法预览')
    return
  }
  previewVisible.value = true
}

// 关闭预览
const handleClosePreview = () => {
  previewVisible.value = false
}

// 打印预览
const handlePrint = () => {
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>模板预览 - ${templateName.value}</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              line-height: 1.6; 
              margin: 20px; 
            }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          ${templateContent.value}
        </body>
      </html>
    `)
    printWindow.document.close()
    printWindow.print()
  }
}

// 内容变化处理
const handleContentChange = (content: string) => {
  hasUnsavedChanges.value = true
}

// 加载模板数据
const loadTemplate = async () => {
  if (templateId.value && templateId.value !== 'new') {
    try {
      // 这里应该调用API加载模板数据
      console.log('加载模板:', templateId.value)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟加载的模板内容
      templateContent.value = `
        <h1>医院超声检查报告单</h1>
        <h2>患者基本信息</h2>
        <p><strong>姓名：</strong>_______________ <strong>性别：</strong>_______________ <strong>年龄：</strong>_______________</p>
        <p><strong>科室：</strong>_______________ <strong>检查时间：</strong>_______________</p>
        
        <h2>超声所见</h2>
        <p>请在此处填写超声检查的详细所见...</p>
        
        <h2>超声提示</h2>
        <p>请在此处填写超声诊断提示...</p>
        
        <h2>医生签名</h2>
        <p><strong>检查医生：</strong>_______________ <strong>报告医生：</strong>_______________ <strong>审核医生：</strong>_______________</p>
      `
    } catch (error) {
      ElMessage.error('加载模板失败')
      console.error('加载模板失败:', error)
    }
  }
}

// 页面离开前确认
const beforeUnload = (e: BeforeUnloadEvent) => {
  if (hasUnsavedChanges.value) {
    e.preventDefault()
    e.returnValue = ''
  }
}

onMounted(() => {
  loadTemplate()
  window.addEventListener('beforeunload', beforeUnload)
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', beforeUnload)
})
</script>

<style lang="scss" scoped>
.template-editor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid var(--el-border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .template-info {
      .template-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .template-description {
        margin: 4px 0 0 0;
        font-size: 14px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
}

.editor-container {
  flex: 1;
  padding: 24px;
  overflow: hidden;
}

:deep(.preview-dialog) {
  .el-dialog {
    max-height: 80vh;
  }
  
  .el-dialog__body {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.preview-content {
  padding: 20px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  
  :deep(h1) {
    text-align: center;
    margin-bottom: 24px;
    color: var(--el-text-color-primary);
  }
  
  :deep(h2) {
    margin: 20px 0 12px 0;
    color: var(--el-text-color-primary);
    border-bottom: 1px solid var(--el-border-color);
    padding-bottom: 8px;
  }
  
  :deep(p) {
    margin: 8px 0;
    line-height: 1.6;
  }
  
  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    
    th, td {
      border: 1px solid var(--el-border-color);
      padding: 8px 12px;
      text-align: left;
    }
    
    th {
      background: var(--el-bg-color-page);
      font-weight: 600;
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    
    .header-left {
      width: 100%;
      justify-content: flex-start;
    }
    
    .header-right {
      width: 100%;
      justify-content: flex-end;
    }
    
    .template-info {
      .template-title {
        font-size: 20px;
      }
    }
  }
  
  .editor-container {
    padding: 16px;
  }
}
</style>
